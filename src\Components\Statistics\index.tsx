// @ts-nocheck
import './Statistics.scss'

import { useEffect, useState } from 'react'
import { useAppSelector } from '../../Hooks'

interface StatisticsData {
  totalPosts: number
  totalArticles: number
  totalPhotos: number
  totalVideos: number
}

const Statistics = () => {
  const userState: any = useAppSelector(state => state.user)
  const [statistics, setStatistics] = useState<StatisticsData>({
    totalPosts: 0,
    totalArticles: 0,
    totalPhotos: 0,
    totalVideos: 0
  })

  // Mock data for now - this can be replaced with actual API calls later
  useEffect(() => {
    if (userState?.value?.data?._id) {
      // Simulate fetching statistics data with more impressive numbers
      setStatistics({
        totalPosts: 247,
        totalArticles: 89,
        totalPhotos: 1234,
        totalVideos: 156
      })
    }
  }, [userState?.value?.data?._id])

  const statisticsCards = [
    {
      title: 'Total Posts',
      count: statistics.totalPosts,
      icon: 'fa fa-edit',
      gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      description: 'Published content'
    },
    {
      title: 'Total Articles',
      count: statistics.totalArticles,
      icon: 'fa fa-file-text-o',
      gradient: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
      description: 'Written articles'
    },
    {
      title: 'Total Photos',
      count: statistics.totalPhotos.toLocaleString(),
      icon: 'fa fa-camera',
      gradient: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
      description: 'Images uploaded'
    },
    {
      title: 'Total Videos',
      count: statistics.totalVideos,
      icon: 'fa fa-video-camera',
      gradient: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
      description: 'Video content'
    }
  ]

  if (!userState?.value?.data?._id) {
    return null
  }

  return (
    <div className="Statistics">
      <div className="statistics-header">
        <h2>Content Overview</h2>
        <p>Your content statistics at a glance</p>
      </div>
      <div className="statistics-grid">
        {statisticsCards.map((card, index) => (
          <div key={index} className="statistics-card">
            <div className="card-background" style={{ background: card.gradient }}></div>
            <div className="card-content">
              <div className="card-header">
                <div className="card-icon">
                  <i className={card.icon}></i>
                </div>
                <div className="card-title">{card.title}</div>
              </div>
              <div className="card-body">
                <div className="card-count">{card.count}</div>
                <div className="card-description">{card.description}</div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

export default Statistics
