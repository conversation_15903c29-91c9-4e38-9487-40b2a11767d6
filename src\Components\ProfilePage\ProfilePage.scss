// Gray scale variables
$gray-100: #f8f9fa;
$gray-200: #e9ecef;
$gray-300: #dee2e6;
$gray-400: #ced4da;
$gray-500: #adb5bd;
$gray-600: #6c757d;
$gray-700: #495057;
$gray-750: #3a3f47;
$gray-800: #343a40;
$gray-900: #212529;

// Grid breakpoints
$grid-breakpoints: (
  xs: 0,
  sm: 576px,
  md: 768px,
  lg: 992px,
  xl: 1200px,
  xxl: 1400px
);

@import '../../Assets/Styles/colors';
@import '../../Assets/Styles/typography';
@import '../../Assets/Styles/mixins';

.profile-page {
  min-height: 100vh;
  background: $gray-800;
  width: 100%;
  overflow-y: auto;

  .profile-header-bar {
    background: $white;
    padding: 1rem 2rem;
    border-bottom: 1px solid $gray-300;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: 0 2px 4px rgba($gray-900, 0.1);

    @media (max-width: map-get($grid-breakpoints, md)) {
      padding: 1rem;
      justify-content: space-between;
      position: relative;
    }

    .back-button {
      background: transparent;
      border: 1px solid $gray-400;
      color: $gray-700;
      padding: 0.5rem 1rem;
      border-radius: 20px;
      font-size: 0.9rem;
      display: flex;
      align-items: center;
      gap: 0.5rem;
      transition: all 0.2s ease;
      cursor: pointer;

      &:hover {
        background: $gray-100;
        border-color: $gray-500;
      }

      i {
        font-size: 1rem;
      }

      @media (max-width: map-get($grid-breakpoints, md)) {
        margin-right: auto;
      }
    }



    .edit-profile-btn {
      background: transparent;
      border: 1px solid $success;
      color: $success;
      padding: 0.5rem 1rem;
      border-radius: 20px;
      font-size: 0.9rem;
      display: flex;
      align-items: center;
      gap: 0.5rem;
      transition: all 0.2s ease;

      &:hover {
        background: $success;
        color: $white;
      }

      i {
        font-size: 1rem;
      }

      @media (max-width: map-get($grid-breakpoints, md)) {
        margin-left: auto;
      }
    }
  }

  .profile-body {
    padding: 0;
    flex: 1;
  }

  // When no header bar is present, add top padding
  &.no-header .profile-body {
    padding-top: 2rem;

    @media (max-width: map-get($grid-breakpoints, md)) {
      padding-top: 1.5rem;
    }

    @media (max-width: map-get($grid-breakpoints, sm)) {
      padding-top: 1rem;
    }
  }

  .profile-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem 1rem;
    min-height: calc(100vh - 80px); // Ensure content takes full height

    @media (max-width: map-get($grid-breakpoints, lg)) {
      max-width: 100%;
    }

    @media (max-width: map-get($grid-breakpoints, md)) {
      padding: 1rem;
    }

    .profile-header {
      background: $white;
      padding: 2rem;
      border-radius: 12px;
      display: flex;
      align-items: center;
      gap: 2rem;
      margin-bottom: 2rem;
      box-shadow: 0 2px 8px rgba($gray-900, 0.08);

      @media (max-width: map-get($grid-breakpoints, md)) {
        flex-direction: column;
        text-align: center;
        padding: 1.5rem;
        gap: 1rem;
      }

      .profile-image {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        overflow: hidden;
        background: $gray-200;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .profile-initial {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 3rem;
          color: $success;
          background: #e8f5e9;
        }

        @media (max-width: map-get($grid-breakpoints, md)) {
          width: 100px;
          height: 100px;
        }

        @media (max-width: map-get($grid-breakpoints, sm)) {
          width: 80px;
          height: 80px;
        }
      }

      .profile-info {
        h2 {
          font-size: 1.8rem;
          margin-bottom: 0.5rem;
          color: $gray-800;

          @media (max-width: map-get($grid-breakpoints, md)) {
            font-size: 1.5rem;
          }

          @media (max-width: map-get($grid-breakpoints, sm)) {
            font-size: 1.3rem;
          }
        }

        .email {
          font-size: 1rem;
          color: $gray-600;
          margin: 0;
        }

        .profile-description {
          margin-top: 1rem;

          .description-display {
            position: relative;
            padding: 1rem;
            background: $gray-100;
            border-radius: 8px;
            border: 1px solid $gray-300;
            transition: all 0.2s ease;

            &:hover {
              background: $gray-200;
              border-color: $gray-400;

              .edit-description-btn {
                opacity: 1;
              }
            }

            .description-text {
              font-size: 0.95rem;
              color: $gray-700;
              line-height: 1.5;
              margin: 0;
              padding-right: 2rem;
            }

            .edit-description-btn {
              position: absolute;
              top: 0.5rem;
              right: 0.5rem;
              background: none;
              border: none;
              color: $gray-500;
              font-size: 1rem;
              cursor: pointer;
              opacity: 0;
              transition: all 0.2s ease;
              padding: 0.5rem;
              border-radius: 4px;

              &:hover {
                color: $success;
                background: rgba($success, 0.1);
              }
            }
          }

          .description-edit {
            .description-textarea {
              width: 100%;
              padding: 1rem;
              border: 2px solid $gray-300;
              border-radius: 8px;
              font-size: 0.95rem;
              color: $gray-700;
              resize: vertical;
              min-height: 100px;
              font-family: inherit;
              line-height: 1.5;

              &::placeholder {
                color: $gray-500;
              }

              &:focus {
                outline: none;
                border-color: $success;
                box-shadow: 0 0 0 3px rgba($success, 0.1);
              }
            }

            .description-actions {
              display: flex;
              gap: 0.5rem;
              margin-top: 0.5rem;
              justify-content: flex-end;

              button {
                padding: 0.5rem 1rem;
                border: none;
                border-radius: 6px;
                font-size: 0.9rem;
                cursor: pointer;
                transition: all 0.2s ease;
                font-weight: 500;

                &.save-btn {
                  background: $success;
                  color: $white;

                  &:hover {
                    background: darken($success, 10%);
                  }
                }

                &.cancel-btn {
                  background: $gray-300;
                  color: $gray-700;

                  &:hover {
                    background: $gray-400;
                  }
                }
              }
            }
          }

          @media (max-width: map-get($grid-breakpoints, md)) {
            .description-display .description-text {
              font-size: 0.9rem;
              padding-right: 1.5rem;
            }

            .description-edit .description-textarea {
              font-size: 0.9rem;
              padding: 0.8rem;
            }
          }
        }
      }
    }

    .qa-section {
      background: $white;
      padding: 2rem;
      padding-bottom: 4rem; // Extra space for indicators
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba($gray-900, 0.08);
      margin-bottom: 2rem;

      @media (max-width: map-get($grid-breakpoints, md)) {
        padding: 1.5rem;
        padding-bottom: 3.5rem;
      }

      @media (max-width: map-get($grid-breakpoints, sm)) {
        padding: 1rem;
        padding-bottom: 3rem;
      }

      h3 {
        font-size: 1.4rem;
        margin-bottom: 1.5rem;
        color: $gray-800;
        font-weight: 600;

        @media (max-width: map-get($grid-breakpoints, md)) {
          font-size: 1.2rem;
        }
      }

      .qa-carousel {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 1rem;
        padding: 2rem 0;
        width: 100%;

        .carousel-nav {
          background: $white;
          border: 2px solid $gray-300;
          border-radius: 50%;
          width: 50px;
          height: 50px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: all 0.3s ease;
          z-index: 10;
          flex-shrink: 0;

          &:hover {
            background: $success;
            border-color: $success;
            color: $white;
            transform: scale(1.1);
          }

          span {
            font-size: 1.8rem;
            font-weight: bold;
            line-height: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 100%;
          }

          &.prev {
            margin-right: 1rem;

            span {
              margin-left: -2px; // Fine-tune left arrow alignment
            }
          }

          &.next {
            margin-left: 1rem;

            span {
              margin-right: -2px; // Fine-tune right arrow alignment
            }
          }

          @media (max-width: map-get($grid-breakpoints, md)) {
            width: 40px;
            height: 40px;

            span {
              font-size: 1.5rem;
            }
          }
        }

        .carousel-container {
          flex: 1;
          overflow: hidden;
          position: relative;
          height: 300px;

          @media (max-width: map-get($grid-breakpoints, md)) {
            height: 250px;
          }
        }

        .carousel-track {
          display: flex;
          transition: transform 0.5s ease-in-out;
          height: 100%;
        }

        .qa-card {
          min-width: 100%;
          background: $gray-800;
          padding: 2rem;
          border-radius: 12px;
          border: 1px solid $gray-700;
          transition: all 0.3s ease;
          display: flex;
          flex-direction: column;
          justify-content: center;
          position: relative;
          cursor: pointer;

          &.active {
            background: $gray-900;
            border-color: $success;
            box-shadow: 0 8px 25px rgba($success, 0.25);
            transform: scale(1.02);
            cursor: default;
          }

          &.inactive {
            opacity: 0.8;
            transform: scale(0.95);
            background: $gray-700;

            &:hover {
              opacity: 1;
              transform: scale(0.98);
              background: $gray-750;
            }
          }

          &.hidden {
            opacity: 0;
          }

          h4 {
            font-size: 1.3rem;
            color: $white;
            margin-bottom: 1.5rem;
            font-weight: 600;
            text-align: center;
            border-bottom: 2px solid $success;
            padding-bottom: 1rem;

            @media (max-width: map-get($grid-breakpoints, md)) {
              font-size: 1.1rem;
              margin-bottom: 1rem;
            }
          }

          p {
            font-size: 1.1rem;
            color: $gray-200;
            margin: 0;
            padding: 1rem;
            border-radius: 8px;
            position: relative;
            cursor: text;
            min-height: 80px;
            background: rgba($gray-900, 0.6);
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;

            &:hover {
              background: rgba($success, 0.2);
              color: $white;

              .edit-icon {
                opacity: 1;
              }
            }

            .edit-icon {
              position: absolute;
              right: 1rem;
              top: 1rem;
              color: $success;
              opacity: 0;
              transition: opacity 0.2s ease;
              font-size: 1.2rem;
            }

            @media (max-width: map-get($grid-breakpoints, md)) {
              font-size: 1rem;
              padding: 0.8rem;
              min-height: 60px;
            }
          }

          textarea {
            width: 100%;
            padding: 1rem;
            border: 2px solid $success;
            border-radius: 8px;
            font-size: 1.1rem;
            color: $white;
            resize: vertical;
            min-height: 80px;
            background: $gray-900;

            &::placeholder {
              color: $gray-400;
            }

            &:focus {
              outline: none;
              border-color: lighten($success, 10%);
              box-shadow: 0 0 0 3px rgba($success, 0.3);
              background: $gray-800;
            }

            @media (max-width: map-get($grid-breakpoints, md)) {
              font-size: 1rem;
              padding: 0.8rem;
              min-height: 60px;
            }
          }
        }

        .carousel-indicators {
          position: absolute;
          bottom: -3rem;
          // left: 50%;
          // transform: translateX(-50%);
          display: flex;
          justify-content: center;
          align-items: center;
          gap: 0.8rem;
          // width: 100%;

          .indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: none;
            background: $gray-300;
            cursor: pointer;
            transition: all 0.3s ease;
            flex-shrink: 0;

            &.active {
              background: $success;
              transform: scale(1.3);
              box-shadow: 0 0 0 3px rgba($success, 0.2);
            }

            &:hover:not(.active) {
              background: $gray-500;
              transform: scale(1.1);
            }
          }

          @media (max-width: map-get($grid-breakpoints, md)) {
            bottom: -2.5rem;
            gap: 0.6rem;

            .indicator {
              width: 10px;
              height: 10px;
            }
          }
        }
      }
    }

    .center-section-wrapper {
      background: $white;
      padding: 2rem;
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba($gray-900, 0.08);
      margin-bottom: 2rem;

      @media (max-width: map-get($grid-breakpoints, md)) {
        padding: 1.5rem;
      }

      @media (max-width: map-get($grid-breakpoints, sm)) {
        padding: 1rem;
      }

      .section-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        cursor: pointer;
        padding: 1rem 0;
        margin-bottom: 0;
        transition: all 0.2s ease;
        border-bottom: 1px solid $gray-200;

        &:hover {
          background: rgba($gray-200, 0.3);
          border-radius: 8px;
          padding: 1rem;
          margin: 0 -1rem;
          border-bottom: 1px solid transparent;
        }

        h3 {
          font-size: 1.4rem;
          margin: 0;
          color: $gray-800;
          font-weight: 600;

          @media (max-width: map-get($grid-breakpoints, md)) {
            font-size: 1.2rem;
          }
        }

        .toggle-arrow {
          font-size: 1.5rem !important;
          color: $gray-700 !important;
          transition: all 0.3s ease;
          font-weight: bold;
          min-width: 30px;
          text-align: center;
          display: inline-block !important;
          visibility: visible !important;
          opacity: 1 !important;
          cursor: pointer;
          user-select: none;
          line-height: 1;

          &:hover {
            color: $gray-900 !important;
            transform: scale(1.2);
          }
        }
      }

      .center-section-content {
        animation: slideDown 0.3s ease-out;
        padding-top: 1rem;

        .CenterSection {
          border: none;
          padding: 0;
          max-height: none;
          overflow: visible;
        }
      }
    }
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

