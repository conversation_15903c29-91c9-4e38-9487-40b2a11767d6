
@import 'bootstrap/scss/bootstrap';
@import 'bootstrap/scss/mixins';
@import '../../Assets/Styles/typography';
@import '../../Assets/Styles/colors';
@import '../../Assets/Styles/mixins';

.Statistics {
  padding: 0 80px 40px;
  width: 100%;

  @include media-breakpoint-down(md) {
    padding: 0 15px 30px;
  }

  @include media-breakpoint-only(md) {
    padding: 0 40px 35px;
  }

  .statistics-header {
    margin-bottom: 30px;
    text-align: center;

    h2 {
      font: {
        family: $font-family-base;
        weight: $font-weight-bold;
        size: 32px;
      }
      color: $heading;
      margin: 0 0 8px 0;
      text-transform: uppercase;
      letter-spacing: 1px;

      @include media-breakpoint-down(md) {
        font-size: 26px;
      }

      @include media-breakpoint-down(sm) {
        font-size: 22px;
      }
    }

    p {
      font: {
        family: $font-family-base;
        weight: $font-weight-regular;
        size: 16px;
      }
      color: $paragraph-2;
      margin: 0;
      opacity: 0.8;

      @include media-breakpoint-down(md) {
        font-size: 14px;
      }
    }
  }

  .statistics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 25px;
    margin-bottom: 20px;

    @include media-breakpoint-down(lg) {
      grid-template-columns: repeat(2, 1fr);
      gap: 20px;
    }

    @include media-breakpoint-down(md) {
      grid-template-columns: repeat(2, 1fr);
      gap: 18px;
    }

    @include media-breakpoint-down(sm) {
      grid-template-columns: 1fr;
      gap: 20px;
    }
  }

  .statistics-card {
    background: rgba(20, 25, 35, 0.95);
    border-radius: 16px;
    padding: 2px;
    position: relative;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    min-height: 140px;
    backdrop-filter: blur(10px);

    @include media-breakpoint-down(md) {
      min-height: 120px;
      border-radius: 12px;
    }

    @include media-breakpoint-down(sm) {
      min-height: 130px;
    }

    // Gradient border using pseudo-element
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      border-radius: 16px;
      padding: 2px;
      background: linear-gradient(135deg, #F8B195, #355C7D, #6C7A89, #29cc97);
      mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
      mask-composite: xor;
      -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
      -webkit-mask-composite: xor;
      z-index: 0;
    }

    &:hover {
      transform: translateY(-8px) scale(1.02);
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);

      &::before {
        background: linear-gradient(135deg, #F8B195, #355C7D, #6C7A89, #29cc97, #F8B195);
        animation: borderRotate 3s linear infinite;
      }

      .card-background {
        opacity: 0.15;
        transform: scale(1.1);
      }

      .card-count {
        transform: scale(1.05);
      }
    }

    .card-background {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      opacity: 0.08;
      transition: all 0.4s ease;
      z-index: 1;
    }

    .card-content {
      position: relative;
      z-index: 2;
      padding: 24px;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      background: rgba(20, 25, 35, 0.95);
      border-radius: 14px;
      backdrop-filter: blur(10px);

      @include media-breakpoint-down(md) {
        padding: 20px;
        border-radius: 10px;
      }

      @include media-breakpoint-down(sm) {
        padding: 22px;
      }
    }

    .card-header {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 16px;

      @include media-breakpoint-down(md) {
        gap: 10px;
        margin-bottom: 12px;
      }
    }

    .card-icon {
      width: 48px;
      height: 48px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 12px;
      backdrop-filter: blur(10px);

      @include media-breakpoint-down(md) {
        width: 42px;
        height: 42px;
        border-radius: 10px;
      }

      i {
        font-size: 22px;
        color: rgba(255, 255, 255, 0.9);

        @include media-breakpoint-down(md) {
          font-size: 20px;
        }
      }
    }

    .card-title {
      font: {
        family: $font-family-base;
        weight: $font-weight-medium;
        size: 16px;
      }
      color: rgba(255, 255, 255, 0.8);
      text-transform: uppercase;
      letter-spacing: 0.5px;

      @include media-breakpoint-down(md) {
        font-size: 14px;
      }
    }

    .card-body {
      flex: 1;
    }

    .card-count {
      font: {
        family: $font-family-base;
        weight: $font-weight-bold;
        size: 42px;
      }
      color: $white;
      line-height: 1;
      margin-bottom: 8px;
      transition: transform 0.3s ease;

      @include media-breakpoint-down(md) {
        font-size: 36px;
        margin-bottom: 6px;
      }

      @include media-breakpoint-down(sm) {
        font-size: 32px;
      }
    }

    .card-description {
      font: {
        family: $font-family-base;
        weight: $font-weight-regular;
        size: 14px;
      }
      color: rgba(255, 255, 255, 0.6);
      line-height: 1.3;
      text-transform: capitalize;

      @include media-breakpoint-down(md) {
        font-size: 13px;
      }
    }
  }

  // Animation for loading state
  .statistics-card.loading {
    .card-count {
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
      background-size: 200% 100%;
      animation: shimmer 1.5s infinite;
      border-radius: 6px;
      height: 42px;
      width: 80px;

      @include media-breakpoint-down(md) {
        height: 36px;
        width: 70px;
      }
    }

    .card-description {
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
      background-size: 200% 100%;
      animation: shimmer 1.5s infinite;
      border-radius: 4px;
      height: 14px;
      width: 100px;
    }
  }

  @keyframes shimmer {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }

  @keyframes borderRotate {
    0% {
      background: linear-gradient(135deg, #F8B195, #355C7D, #6C7A89, #29cc97);
    }
    25% {
      background: linear-gradient(135deg, #355C7D, #6C7A89, #29cc97, #F8B195);
    }
    50% {
      background: linear-gradient(135deg, #6C7A89, #29cc97, #F8B195, #355C7D);
    }
    75% {
      background: linear-gradient(135deg, #29cc97, #F8B195, #355C7D, #6C7A89);
    }
    100% {
      background: linear-gradient(135deg, #F8B195, #355C7D, #6C7A89, #29cc97);
    }
  }

  // Additional visual enhancements
  .statistics-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    z-index: 3;
  }

  .statistics-card::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.03) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 1;
  }

  .statistics-card:hover::after {
    opacity: 1;
  }
}


