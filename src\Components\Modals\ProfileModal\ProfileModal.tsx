// @ts-nocheck
import React, { useState } from 'react';
import { Modal } from 'react-bootstrap';
import { useDispatch } from 'react-redux';
import { useAppSelector } from '../../../Hooks';
import { login } from '../../../Redux/slices/userSlice';
import { uploadMedia } from '../../../Redux/thunks/pageDetailThunk';
import { updateUserInfo } from '../../../Redux/thunks/settingsThunk';
import RoundedCropper from '../CropperPopup/RoundedCropper';
import './ProfileModal.scss';

interface ProfileModalProps {
  show: boolean;
  handleClose: () => void;
  pageImage?: string;
  userName?: string;
  isInSidePanel?: boolean;
}

const profileQuestions = [
  { 
    id: 1, 
    original: "Who was the biggest influence in your life?",
    firstPerson: "Who was the biggest influence in my life?",
    defaultAnswer: "I was deeply influenced by..." 
  },
  { 
    id: 2, 
    original: "What major event or realization shaped who you are?",
    firstPerson: "What major event or realization shaped who I am?",
    defaultAnswer: "A pivotal moment in my life was..." 
  },
  { 
    id: 3, 
    original: "Who was your closest friend growing up?",
    firstPerson: "Who was my closest friend growing up?",
    defaultAnswer: "My closest childhood friend was..." 
  },
  { 
    id: 4, 
    original: "What was the most important lesson you learned as a parent?",
    firstPerson: "What was the most important lesson I learned as a parent?",
    defaultAnswer: "The most valuable parenting lesson I learned..." 
  },
  { 
    id: 5, 
    original: "What is the best advice you could give your child?",
    firstPerson: "What is the best advice I could give my child?",
    defaultAnswer: "The most important advice I would give is..." 
  },
  { 
    id: 6, 
    original: "What phrase has kept you afloat during hard times?",
    firstPerson: "What phrase has kept me afloat during hard times?",
    defaultAnswer: "The words that helped me through difficult times..." 
  },
  { 
    id: 7, 
    original: "What advice do you have about growing older?",
    firstPerson: "What advice do I have about growing older?",
    defaultAnswer: "My perspective on aging is..." 
  },
  { 
    id: 8, 
    original: "What are some of your favorite memories growing up?",
    firstPerson: "What are some of my favorite memories growing up?",
    defaultAnswer: "My cherished childhood memories include..." 
  },
  { 
    id: 9, 
    original: "When life was difficult, what did you do to overcome it?",
    firstPerson: "When life was difficult, what did I do to overcome it?",
    defaultAnswer: "I overcame life's challenges by..." 
  },
  { 
    id: 10, 
    original: "What is one thing you haven't done you regret?",
    firstPerson: "What is one thing I haven't done I regret?",
    defaultAnswer: "One regret I carry is..." 
  },
  { 
    id: 11, 
    original: "What are you most proud of having done in life?",
    firstPerson: "What am I most proud of having done in life?",
    defaultAnswer: "My proudest achievement is..." 
  },
  { 
    id: 12, 
    original: "Do you have any superstitions?",
    firstPerson: "Do I have any superstitions?",
    defaultAnswer: "When it comes to superstitions..." 
  },
  { 
    id: 13, 
    original: "What do you hold sacred?",
    firstPerson: "What do I hold sacred?",
    defaultAnswer: "The things I hold most sacred are..." 
  },
  { 
    id: 14, 
    original: "Did you have a nickname? What is the story behind it?",
    firstPerson: "Did I have a nickname? What is the story behind it?",
    defaultAnswer: "The story of my nickname..." 
  },
  { 
    id: 15, 
    original: "If you could redo any period in your life, what would it be and why?",
    firstPerson: "If I could redo any period in my life, what would it be and why?",
    defaultAnswer: "If I could go back in time..." 
  },
  { 
    id: 16, 
    original: "What is your favorite compliment to receive? Why?",
    firstPerson: "What is my favorite compliment to receive? Why?",
    defaultAnswer: "The compliment that means the most to me..." 
  }
];

const ProfileModal: React.FC<ProfileModalProps> = ({ show, handleClose, pageImage, userName, isInSidePanel = false }) => {
  const dispatch = useDispatch();
  const userState = useAppSelector(state => state.user);
  const isLoggedIn = !!userState?.value?.data?._id;
  const [answers, setAnswers] = useState(
    profileQuestions.reduce((acc, q) => ({ ...acc, [q.id]: q.defaultAnswer }), {})
  );
  const [isEditing, setIsEditing] = useState<number | null>(null);

  // Image upload states
  const [img, setImg] = useState({
    url: pageImage || '',
    file: '',
    selected_img: '',
  });
  const [showCropper, setShowCropper] = useState(false);
  const [imgValidation, setImgValidation] = useState('');

  const handleAnswerChange = (questionId: number, value: string) => {
    setAnswers(prev => ({
      ...prev,
      [questionId]: value
    }));
  };

  const selectImg = () => {
    const input = document.getElementById('profile-image-input');
    if (input) {
      input?.click();
    }
  };

  const typeValidation = (event: any) => {
    setImg((data: any) => {
      return {
        ...data,
        file: '',
      };
    });
    const selectedFile = event.target.files[0];
    const imageFile = selectedFile;
    if (!imageFile.name.match(/\.(jpg|jpeg|png|webp)$/)) {
      setImgValidation('Only jpg | jpeg | png | webp images allowed.');
      return false;
    }
    setImgValidation('');
    if (event.target.files?.length) {
      setImg({
        ...img,
        selected_img: URL.createObjectURL(event?.target?.files[0]),
      });

      const reader: any = new FileReader();
      const file: any = selectedFile;
      reader.readAsDataURL(file);
      setImg((data: any) => {
        return { ...data, file };
      });
      reader.onload = () => {
        setImg((data: any) => {
          return { ...data, selected_img: reader?.result };
        });
        setShowCropper(true);
      };
    }
  };

  const CroppedImage = (e: any) => {
    if (e && e !== 'cancel') {
      setImg((data: any) => {
        return { ...data, url: URL.createObjectURL(e), file: e };
      });
      // Upload the image
      uploadProfileImage(e);
    }
    setShowCropper(false);
  };

  const uploadProfileImage = async (file: any) => {
    if (file && userState?.value?.data?._id) {
      const formData: any = new FormData();
      formData.append('media', file);

      try {
        const response = await uploadMedia(formData, userState?.value?.data?._id);
        if (response?.data?.data[0]?.url) {
          const newImageUrl = response?.data?.data[0]?.url;

          // Update user profile with new image
          const payload = {
            ...userState?.value?.data,
            image: newImageUrl,
          };
          delete payload.email;
          dispatch(updateUserInfo(payload, false)); // Don't reload page

          // Update local state immediately for UI feedback
          dispatch(login({
            ...userState?.value,
            data: { ...userState?.value?.data, image: newImageUrl }
          }));
        }
      } catch (error) {
        console.error('Error uploading profile image:', error);
        setImgValidation('Failed to upload image. Please try again.');
      }
    }
  };

  const renderProfileContent = () => (
    <div className="profile-content">
      <div className="profile-header">
        <div className="profile-image">
          {img.url || pageImage ? (
            <>
              <img src={img.url || pageImage} alt="Profile" />
              {isLoggedIn && (
                <span className="upload-overlay" onClick={selectImg}>
                  <i className="fa fa-camera"></i>
                </span>
              )}
            </>
          ) : (
            <div className="profile-initial" onClick={isLoggedIn ? selectImg : undefined}>
              {userName?.charAt(0) || 'U'}
              {isLoggedIn && (
                <span className="upload-overlay">
                  <i className="fa fa-camera"></i>
                </span>
              )}
            </div>
          )}
          {isLoggedIn && (
            <input
              type="file"
              id="profile-image-input"
              accept="image/*"
              onChange={typeValidation}
              style={{ display: 'none' }}
            />
          )}
        </div>
        <div className="profile-info">
          <h2>{userName || 'User Name'}</h2>
          {isLoggedIn && (
            <p className="email">{userState?.value?.data?.email}</p>
          )}
        </div>
      </div>

      <div className="qa-section">
        <h3>Get to Know Me</h3>
        <div className="qa-grid">
          {profileQuestions.map((q) => (
            <div key={q.id} className="qa-card">
              <h4>{isEditing ? q.original : q.firstPerson}</h4>
              {isEditing === q.id ? (
                <textarea
                  value={answers[q.id]}
                  onChange={(e) => handleAnswerChange(q.id, e.target.value)}
                  rows={3}
                  placeholder="Share your answer..."
                />
              ) : (
                <p onClick={() => isLoggedIn && setIsEditing(q.id)}>
                  {answers[q.id]}
                  {isLoggedIn && isEditing === -1 && (
                    <i className="fa fa-edit edit-icon"></i>
                  )}
                </p>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  if (isInSidePanel) {
    return (
      <div className="profile-content-wrapper">
        <div className="profile-header-bar">
          <h4>Profile</h4>
          {isLoggedIn && (
            <button
              className="edit-profile-btn"
              onClick={() => setIsEditing(isEditing ? null : -1)}
            >
              <i className="fa fa-edit"></i>
              {isEditing ? 'Done' : 'Edit Profile'}
            </button>
          )}
        </div>
        {renderProfileContent()}
        {imgValidation && (
          <div className="error-message">
            {imgValidation}
          </div>
        )}
        {showCropper && (
          <RoundedCropper
            show={showCropper}
            selectedImg={img.selected_img}
            setCroppedImage={CroppedImage}
            croppershap={'round'}
          />
        )}
      </div>
    );
  }

  return (
    <Modal
      show={show}
      onHide={handleClose}
      className="profile-modal"
      fullscreen
    >
      <Modal.Header closeButton>
        <Modal.Title>About Me</Modal.Title>
        {isLoggedIn && (
          <button
            className="edit-profile-btn"
            onClick={() => setIsEditing(isEditing ? null : -1)}
          >
            <i className="fa fa-edit"></i>
            {isEditing ? 'Done' : 'Edit Profile'}
          </button>
        )}
      </Modal.Header>
      <Modal.Body>
        {renderProfileContent()}
        {imgValidation && (
          <div className="error-message">
            {imgValidation}
          </div>
        )}
      </Modal.Body>
      {showCropper && (
        <RoundedCropper
          show={showCropper}
          selectedImg={img.selected_img}
          setCroppedImage={CroppedImage}
          croppershap={'round'}
        />
      )}
    </Modal>
  );
};

export default ProfileModal; 